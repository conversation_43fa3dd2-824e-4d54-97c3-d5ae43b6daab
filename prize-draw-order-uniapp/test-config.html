<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>首页配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 375px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .preview-container {
            min-height: 600px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            background-size: cover;
            background-position: center;
            padding: 20px;
            position: relative;
        }
        .preview-container.has-bg-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }
        .preview-container > * {
            position: relative;
            z-index: 2;
        }
        .merchant-info {
            text-align: center;
            margin-bottom: 40px;
        }
        .logo-container {
            margin-bottom: 15px;
        }
        .logo-img {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }
        .welcome-text {
            font-size: 16px;
            color: #fff;
            margin-bottom: 10px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        .merchant-name {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            margin-bottom: 10px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        .merchant-address {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }
        .config-panel {
            padding: 20px;
        }
        .config-item {
            margin-bottom: 15px;
        }
        .config-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-item input, .config-item textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        .config-item input[type="file"] {
            padding: 4px;
        }
        button {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #5a6fd8;
        }
    </style>
</head>
<body>
    <h1>首页配置功能测试</h1>
    
    <div class="container">
        <div id="preview" class="preview-container">
            <div class="merchant-info">
                <div class="logo-container" id="logoContainer" style="display: none;">
                    <img id="logoImg" class="logo-img" src="" alt="Logo">
                </div>
                <div class="welcome-text" id="welcomeText" style="display: none;"></div>
                <div class="merchant-name" id="merchantName">测试商家</div>
                <div class="merchant-address" id="merchantAddress">测试地址123号</div>
            </div>
        </div>
        
        <div class="config-panel">
            <h3>配置测试</h3>
            
            <div class="config-item">
                <label>背景图片:</label>
                <input type="file" id="backgroundImage" accept="image/*">
            </div>
            
            <div class="config-item">
                <label>Logo图片:</label>
                <input type="file" id="logoImage" accept="image/*">
            </div>
            
            <div class="config-item">
                <label>欢迎语:</label>
                <input type="text" id="welcomeTextInput" placeholder="请输入欢迎语">
            </div>
            
            <div class="config-item">
                <label>页面标题:</label>
                <input type="text" id="pageTitle" placeholder="请输入页面标题" value="抽奖点餐">
            </div>
            
            <button onclick="applyConfig()">应用配置</button>
            <button onclick="resetConfig()">重置</button>
        </div>
    </div>

    <script>
        function applyConfig() {
            // 背景图片
            const bgFile = document.getElementById('backgroundImage').files[0];
            const preview = document.getElementById('preview');
            
            if (bgFile) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.style.backgroundImage = `url('${e.target.result}')`;
                    preview.classList.add('has-bg-image');
                };
                reader.readAsDataURL(bgFile);
            }
            
            // Logo图片
            const logoFile = document.getElementById('logoImage').files[0];
            const logoContainer = document.getElementById('logoContainer');
            const logoImg = document.getElementById('logoImg');
            
            if (logoFile) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    logoImg.src = e.target.result;
                    logoContainer.style.display = 'block';
                };
                reader.readAsDataURL(logoFile);
            }
            
            // 欢迎语
            const welcomeText = document.getElementById('welcomeTextInput').value;
            const welcomeElement = document.getElementById('welcomeText');
            
            if (welcomeText) {
                welcomeElement.textContent = welcomeText;
                welcomeElement.style.display = 'block';
            } else {
                welcomeElement.style.display = 'none';
            }
            
            // 页面标题
            const pageTitle = document.getElementById('pageTitle').value;
            if (pageTitle) {
                document.title = pageTitle;
            }
        }
        
        function resetConfig() {
            const preview = document.getElementById('preview');
            preview.style.backgroundImage = '';
            preview.classList.remove('has-bg-image');
            
            document.getElementById('logoContainer').style.display = 'none';
            document.getElementById('welcomeText').style.display = 'none';
            
            document.getElementById('backgroundImage').value = '';
            document.getElementById('logoImage').value = '';
            document.getElementById('welcomeTextInput').value = '';
            document.getElementById('pageTitle').value = '抽奖点餐';
            
            document.title = '首页配置测试';
        }
    </script>
</body>
</html>
